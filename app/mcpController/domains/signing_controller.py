#!/usr/bin/env python3
"""
签署域控制器 - 电子签名相关功能
提供合同签署、签署流程管理等造数功能
"""
from fastapi import APIRouter, Query
import logging
from typing import Optional

from mcpService.domains.signing_service import (
    create_signing_flow, add_signer, start_signing,
    query_signing_status, cancel_signing, create_flow_one_step
)

# 配置日志
logger = logging.getLogger(__name__)

# 创建签署域路由器
signing_router = APIRouter(
    prefix="/signing",
    tags=["签署域"],
    responses={404: {"description": "Not found"}}
)


@signing_router.post(
    "/create_signing_flow",
    summary="📝 创建签署流程",
    description="创建电子签名流程",
    operation_id="signing_create_flow"
)
async def create_signing_flow_endpoint(
    flow_name: str,
    document_content: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """创建签署流程"""
    return create_signing_flow(flow_name, document_content, environment)


@signing_router.post(
    "/add_signer",
    summary="👥 添加签署人",
    description="向签署流程添加签署人",
    operation_id="signing_add_signer"
)
async def add_signer_endpoint(
    flow_id: str,
    signer_name: str,
    signer_mobile: str,
    signer_idcard: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """添加签署人"""
    return add_signer(flow_id, signer_name, signer_mobile, signer_idcard, environment)


@signing_router.post(
    "/start_signing",
    summary="🚀 启动签署",
    description="启动签署流程",
    operation_id="signing_start"
)
async def start_signing_endpoint(
    flow_id: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """启动签署流程"""
    return start_signing(flow_id, environment)


@signing_router.post(
    "/query_signing_status",
    summary="📊 查询签署状态",
    description="查询签署流程状态",
    operation_id="signing_query_status"
)
async def query_signing_status_endpoint(
    flow_id: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """查询签署状态"""
    return query_signing_status(flow_id, environment)


@signing_router.post(
    "/cancel_signing",
    summary="❌ 取消签署",
    description="取消签署流程",
    operation_id="signing_cancel"
)
async def cancel_signing_endpoint(
    flow_id: str,
    reason: str = "测试取消",
    environment: Optional[str] = Query(None, description="环境类型")
):
    """取消签署流程"""
    return cancel_signing(flow_id, reason, environment)


@signing_router.post(
    "/create_flow_one_step",
    summary="⚡ 一步创建签署流程",
    description="一步创建完整的签署流程，包含文档、签署人和签署位置信息",
    operation_id="signing_create_flow_one_step"
)
async def create_flow_one_step_endpoint(
    file_id: str,
    file_name: str,
    signer_account_id: str,
    authorized_account_id: str,
    business_scene: str = "一步创建流程",
    pos_page: str = "1",
    pos_x: int = 100,
    pos_y: int = 500,
    sign_type: int = 1,
    third_order_no: str = "mcp_test",
    app_id: Optional[str] = None,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """
    一步创建签署流程

    参数说明:
    - file_id: 文档文件ID
    - file_name: 文档文件名
    - signer_account_id: 签署人账号ID
    - authorized_account_id: 授权账号ID
    - business_scene: 业务场景描述
    - pos_page: 签署位置页码
    - pos_x: 签署位置X坐标
    - pos_y: 签署位置Y坐标
    - sign_type: 签署类型 (1=签名)
    - third_order_no: 第三方订单号
    - app_id: 应用ID，不传则使用环境默认值
    - environment: 环境描述，支持自然语言
    """
    return create_flow_one_step(
        file_id=file_id,
        file_name=file_name,
        signer_account_id=signer_account_id,
        authorized_account_id=authorized_account_id,
        business_scene=business_scene,
        pos_page=pos_page,
        pos_x=pos_x,
        pos_y=pos_y,
        sign_type=sign_type,
        third_order_no=third_order_no,
        app_id=app_id,
        environment=environment
    )

# 导出router供main.py使用
router = signing_router
