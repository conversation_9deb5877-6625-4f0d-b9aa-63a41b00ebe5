#!/usr/bin/env python3
"""
签署域服务 - 电子签名相关功能实现
提供合同签署、签署流程管理等功能
"""
import logging
from typing import Dict, Any, Optional
from mcpService.common.base_service import BaseService

logger = logging.getLogger(__name__)


class SigningService(BaseService):
    """签署服务类"""
    
    def __init__(self):
        super().__init__(domain="signing")


# 全局签署服务实例
signing_service = SigningService()


def create_signing_flow(
    flow_name: str,
    document_content: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建签署流程
    
    Args:
        flow_name: 流程名称
        document_content: 文档内容
        environment: 环境描述，支持自然语言
        
    Returns:
        创建结果
    """
    try:
        request_data = {
            "flowName": flow_name,
            "documentContent": document_content
        }
        
        result = signing_service.make_api_request(
            path="/signing/flow/create",
            data=request_data,
            environment=environment,
            operation="创建签署流程"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"创建签署流程异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"创建签署流程异常: {str(e)}",
            details={"flow_name": flow_name}
        )


def add_signer(
    flow_id: str,
    signer_name: str,
    signer_mobile: str,
    signer_idcard: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    添加签署人
    
    Args:
        flow_id: 流程ID
        signer_name: 签署人姓名
        signer_mobile: 签署人手机号
        signer_idcard: 签署人身份证号
        environment: 环境描述，支持自然语言
        
    Returns:
        添加结果
    """
    try:
        request_data = {
            "flowId": flow_id,
            "signerName": signer_name,
            "signerMobile": signer_mobile,
            "signerIdcard": signer_idcard
        }
        
        result = signing_service.make_api_request(
            path="/signing/signer/add",
            data=request_data,
            environment=environment,
            operation="添加签署人"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"添加签署人异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"添加签署人异常: {str(e)}",
            details={"flow_id": flow_id, "signer_name": signer_name}
        )


def start_signing(
    flow_id: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    启动签署流程
    
    Args:
        flow_id: 流程ID
        environment: 环境描述，支持自然语言
        
    Returns:
        启动结果
    """
    try:
        request_data = {
            "flowId": flow_id
        }
        
        result = signing_service.make_api_request(
            path="/signing/flow/start",
            data=request_data,
            environment=environment,
            operation="启动签署流程"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"启动签署流程异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"启动签署流程异常: {str(e)}",
            details={"flow_id": flow_id}
        )


def query_signing_status(
    flow_id: str,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    查询签署状态
    
    Args:
        flow_id: 流程ID
        environment: 环境描述，支持自然语言
        
    Returns:
        签署状态
    """
    try:
        request_data = {
            "flowId": flow_id
        }
        
        result = signing_service.make_api_request(
            path="/signing/flow/status",
            data=request_data,
            environment=environment,
            operation="查询签署状态"
        )
        
        return result
        
    except Exception as e:
        logger.error(f"查询签署状态异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"查询签署状态异常: {str(e)}",
            details={"flow_id": flow_id}
        )


def cancel_signing(
    flow_id: str,
    reason: str = "测试取消",
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    取消签署流程

    Args:
        flow_id: 流程ID
        reason: 取消原因
        environment: 环境描述，支持自然语言

    Returns:
        取消结果
    """
    try:
        request_data = {
            "flowId": flow_id,
            "reason": reason
        }

        result = signing_service.make_api_request(
            path="/signing/flow/cancel",
            data=request_data,
            environment=environment,
            operation="取消签署流程"
        )

        return result

    except Exception as e:
        logger.error(f"取消签署流程异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"取消签署流程异常: {str(e)}",
            details={"flow_id": flow_id, "reason": reason}
        )


def create_flow_one_step(
    file_id: str,
    file_name: str,
    signer_account_id: str,
    authorized_account_id: str,
    business_scene: str = "一步创建流程",
    pos_page: str = "1",
    pos_x: int = 100,
    pos_y: int = 500,
    sign_type: int = 1,
    third_order_no: str = "mcp_test",
    app_id: Optional[str] = None,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    """
    一步创建签署流程

    Args:
        file_id: 文档文件ID
        file_name: 文档文件名
        signer_account_id: 签署人账号ID
        authorized_account_id: 授权账号ID
        business_scene: 业务场景描述
        pos_page: 签署位置页码
        pos_x: 签署位置X坐标
        pos_y: 签署位置Y坐标
        sign_type: 签署类型 (1=签名)
        third_order_no: 第三方订单号
        app_id: 应用ID，不传则使用环境默认值
        environment: 环境描述，支持自然语言

    Returns:
        创建结果
    """
    try:
        # 构建完整的请求数据，按照curl中的结构
        request_data = {
            "docs": [
                {
                    "encryption": 0,
                    "fileId": file_id,
                    "fileName": file_name,
                    "source": 0
                }
            ],
            "flowInfo": {
                "autoArchive": True,
                "autoInitiate": True,
                "businessScene": business_scene,
                "flowConfigInfo": {
                    "notifyConfig": {
                        "noticeDeveloperUrl": "http://www.94rg.com",
                        "noticeType": "1,2,4"
                    },
                    "signConfig": {
                        "redirectUrl": "",
                        "signPlatform": "1,2,3,5"
                    }
                },
                "hashSign": False,
                "remark": "MCP工具创建",
                "signValidity": *************
            },
            "signers": [
                {
                    "signOrder": 1,
                    "signerAccount": {
                        "signerAccountId": signer_account_id,
                        "authorizedAccountId": authorized_account_id
                    },
                    "signfields": [
                        {
                            "autoExecute": False,
                            "actorIndentityType": 2,
                            "certId": "",
                            "fileId": file_id,
                            "sealType": "",
                            "posBean": {
                                "posPage": pos_page,
                                "posX": pos_x,
                                "posY": pos_y
                            },
                            "signType": sign_type
                        }
                    ],
                    "thirdOrderNo": third_order_no
                }
            ]
        }

        # 构建请求头
        headers = {
            "X-Tsign-Open-Auth-Mode": "simple",
            "Content-Type": "application/json;charset=UTF-8"
        }

        # 如果指定了app_id，添加到请求头
        if app_id:
            headers["X-Tsign-Open-App-Id"] = app_id

        result = signing_service.make_api_request(
            url="http://in-test-openapi.tsign.cn/api/v3/signflows/createFlowOneStep",
            data=request_data,
            headers=headers,
            environment=environment,
            service="openapi",
            operation="一步创建签署流程"
        )

        return result

    except Exception as e:
        logger.error(f"一步创建签署流程异常: {str(e)}")
        return signing_service.formatter.error(
            message=f"一步创建签署流程异常: {str(e)}",
            details={
                "file_id": file_id,
                "file_name": file_name,
                "signer_account_id": signer_account_id,
                "business_scene": business_scene
            }
        )
